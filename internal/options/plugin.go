// options-------------------------------------
// @file      : plugin.go
// <AUTHOR> Autumn
// @contact   : <EMAIL>
// @time      : 2024/10/17 19:17
// -------------------------------------------

package options

import "context"

type PluginOption struct {
	Name       string
	Module     string
	Parameter  string
	PluginId   string
	ResultFunc func(interface{})
	Custom     interface{}
	TaskId     string
	TaskName   string
	Log        func(msg string, tp ...string)
	Ctx        context.Context
}

// Code generated by 'yaegi extract github.com/Autumn-27/ScopeSentry-Scan/internal/pool'. DO NOT EDIT.

package symbols

import (
	"github.com/Autumn-27/ScopeSentry-Scan/internal/pool"
	"reflect"
)

func init() {
	Symbols["github.com/Autumn-27/ScopeSentry-Scan/internal/pool/pool"] = map[string]reflect.Value{
		// function, constant and variable definitions
		"Initialize":      reflect.ValueOf(pool.Initialize),
		"PoolManage":      reflect.ValueOf(&pool.PoolManage).Elem(),
		"StartMonitoring": reflect.ValueOf(pool.StartMonitoring),

		// type definitions
		"Manager": reflect.ValueOf((*pool.Manager)(nil)),
	}
}

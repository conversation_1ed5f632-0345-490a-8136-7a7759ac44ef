// Code generated by 'yaegi extract github.com/Autumn-27/ScopeSentry-Scan/internal/bigcache'. DO NOT EDIT.

package symbols

import (
	"github.com/Autumn-27/ScopeSentry-Scan/internal/bigcache"
	"reflect"
)

func init() {
	Symbols["github.com/Autumn-27/ScopeSentry-Scan/internal/bigcache/bigcache"] = map[string]reflect.Value{
		// function, constant and variable definitions
		"BigCache":   reflect.ValueOf(&bigcache.BigCache).Elem(),
		"Initialize": reflect.ValueOf(bigcache.Initialize),

		// type definitions
		"BigCacheWrapper": reflect.ValueOf((*bigcache.BigCacheWrapper)(nil)),
	}
}

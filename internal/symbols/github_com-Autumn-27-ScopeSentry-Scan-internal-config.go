// Code generated by 'yaegi extract github.com/Autumn-27/ScopeSentry-Scan/internal/config'. DO NOT EDIT.

package symbols

import (
	"github.com/Autumn-27/ScopeSentry-Scan/internal/config"
	"reflect"
)

func init() {
	Symbols["github.com/Autumn-27/ScopeSentry-Scan/internal/config/config"] = map[string]reflect.Value{
		// function, constant and variable definitions
		"CreateDir":         reflect.ValueOf(config.CreateDir),
		"GetDictId":         reflect.ValueOf(config.GetDictId),
		"InitFilterUrlRe":   reflect.ValueOf(config.InitFilterUrlRe),
		"Initialize":        reflect.ValueOf(config.Initialize),
		"LoadConfig":        reflect.ValueOf(config.LoadConfig),
		"ModulesConfig":     reflect.ValueOf(&config.ModulesConfig).Elem(),
		"ModulesConfigPath": reflect.ValueOf(&config.ModulesConfigPath).Elem(),
		"ModulesInitialize": reflect.ValueOf(config.ModulesInitialize),

		// type definitions
		"AssetHandleConfig":         reflect.ValueOf((*config.AssetHandleConfig)(nil)),
		"AssetMappConfig":           reflect.ValueOf((*config.AssetMappConfig)(nil)),
		"DirScanConfig":             reflect.ValueOf((*config.DirScanConfig)(nil)),
		"ModulesConfigStruct":       reflect.ValueOf((*config.ModulesConfigStruct)(nil)),
		"PortFingerprintConfig":     reflect.ValueOf((*config.PortFingerprintConfig)(nil)),
		"PortScanConfig":            reflect.ValueOf((*config.PortScanConfig)(nil)),
		"PortScanPreparationConfig": reflect.ValueOf((*config.PortScanPreparationConfig)(nil)),
		"SubdomainScanConfig":       reflect.ValueOf((*config.SubdomainScanConfig)(nil)),
		"SubdomainSecurityConfig":   reflect.ValueOf((*config.SubdomainSecurityConfig)(nil)),
		"URLScanConfig":             reflect.ValueOf((*config.URLScanConfig)(nil)),
		"URLSecurityConfig":         reflect.ValueOf((*config.URLSecurityConfig)(nil)),
		"VulnerabilityScanConfig":   reflect.ValueOf((*config.VulnerabilityScanConfig)(nil)),
		"WebCrawlerConfig":          reflect.ValueOf((*config.WebCrawlerConfig)(nil)),
	}
}

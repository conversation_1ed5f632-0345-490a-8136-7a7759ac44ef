// Code generated by 'yaegi extract github.com/Autumn-27/ScopeSentry-Scan/pkg/logger'. DO NOT EDIT.

package symbols

import (
	"github.com/Autumn-27/ScopeSentry-Scan/pkg/logger"
	"reflect"
)

func init() {
	Symbols["github.com/Autumn-27/ScopeSentry-Scan/pkg/logger/logger"] = map[string]reflect.Value{
		// function, constant and variable definitions
		"GetTimeNow":           reflect.ValueOf(logger.GetTimeNow),
		"NewLogger":            reflect.ValueOf(logger.NewLogger),
		"PluginsLog":           reflect.ValueOf(logger.PluginsLog),
		"SendLogToRedis":       reflect.ValueOf(logger.SendLogToRedis),
		"SendPluginLogToRedis": reflect.ValueOf(logger.SendPluginLogToRedis),
		"SlogDebug":            reflect.ValueOf(logger.SlogDebug),
		"SlogDebugLocal":       reflect.ValueOf(logger.SlogDebugLocal),
		"SlogError":            reflect.ValueOf(logger.SlogError),
		"SlogErrorLocal":       reflect.ValueOf(logger.SlogErrorLocal),
		"SlogInfo":             reflect.ValueOf(logger.SlogInfo),
		"SlogInfoLocal":        reflect.ValueOf(logger.SlogInfoLocal),
		"SlogWarn":             reflect.ValueOf(logger.SlogWarn),
		"SlogWarnLocal":        reflect.ValueOf(logger.SlogWarnLocal),
		"ZapLog":               reflect.ValueOf(&logger.ZapLog).Elem(),

		// type definitions
		"Logger": reflect.ValueOf((*logger.Logger)(nil)),
	}
}

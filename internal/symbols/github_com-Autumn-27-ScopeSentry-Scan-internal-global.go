// Code generated by 'yaegi extract github.com/Autumn-27/ScopeSentry-Scan/internal/global'. DO NOT EDIT.

package symbols

import (
	"github.com/Autumn-27/ScopeSentry-Scan/internal/global"
	"reflect"
)

func init() {
	Symbols["github.com/Autumn-27/ScopeSentry-Scan/internal/global/global"] = map[string]reflect.Value{
		// function, constant and variable definitions
		"AbsolutePath":          reflect.ValueOf(&global.AbsolutePath).Elem(),
		"AppConfig":             reflect.ValueOf(&global.AppConfig).Elem(),
		"ConfigDir":             reflect.ValueOf(&global.ConfigDir).Elem(),
		"ConfigPath":            reflect.ValueOf(&global.ConfigPath).Elem(),
		"CustomMapParameter":    reflect.ValueOf(&global.CustomMapParameter).Elem(),
		"CustomParameter":       reflect.ValueOf(&global.CustomParameter).Elem(),
		"DatabaseEnabled":       reflect.ValueOf(&global.DatabaseEnabled).<PERSON><PERSON>(),
		"DictPath":              reflect.ValueOf(&global.DictPath).Elem(),
		"DisallowedURLFilters":  reflect.ValueOf(&global.DisallowedURLFilters).Elem(),
		"ExtDir":                reflect.ValueOf(&global.ExtDir).Elem(),
		"FirstRun":              reflect.ValueOf(&global.FirstRun).Elem(),
		"NotificationApi":       reflect.ValueOf(&global.NotificationApi).Elem(),
		"NotificationConfig":    reflect.ValueOf(&global.NotificationConfig).Elem(),
		"PluginDir":             reflect.ValueOf(&global.PluginDir).Elem(),
		"PocDir":                reflect.ValueOf(&global.PocDir).Elem(),
		"Projects":              reflect.ValueOf(&global.Projects).Elem(),
		"ScanModule":            reflect.ValueOf(&global.ScanModule).Elem(),
		"SensitiveRules":        reflect.ValueOf(&global.SensitiveRules).Elem(),
		"SubdomainTakerFingers": reflect.ValueOf(&global.SubdomainTakerFingers).Elem(),
		"TakeoverFinger":        reflect.ValueOf(&global.TakeoverFinger).Elem(),
		"TmpCustomMapParameter": reflect.ValueOf(&global.TmpCustomMapParameter).Elem(),
		"TmpCustomParameter":    reflect.ValueOf(&global.TmpCustomParameter).Elem(),
		"TmpDir":                reflect.ValueOf(&global.TmpDir).Elem(),
		"VERSION":               reflect.ValueOf(&global.VERSION).Elem(),
		"WebFingers":            reflect.ValueOf(&global.WebFingers).Elem(),

		// type definitions
		"Config":        reflect.ValueOf((*global.Config)(nil)),
		"MongoDBConfig": reflect.ValueOf((*global.MongoDBConfig)(nil)),
		"RedisConfig":   reflect.ValueOf((*global.RedisConfig)(nil)),
	}
}

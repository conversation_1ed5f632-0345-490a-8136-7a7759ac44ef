// Code generated by 'yaegi extract github.com/Autumn-27/ScopeSentry-Scan/internal/results'. DO NOT EDIT.

package symbols

import (
	"github.com/Autumn-27/ScopeSentry-Scan/internal/results"
	"reflect"
)

func init() {
	Symbols["github.com/Autumn-27/ScopeSentry-Scan/internal/results/results"] = map[string]reflect.Value{
		// function, constant and variable definitions
		"Close":                 reflect.ValueOf(results.Close),
		"Duplicate":             reflect.ValueOf(&results.Duplicate).Elem(),
		"Handler":               reflect.ValueOf(&results.Handler).Elem(),
		"InitializeDuplicate":   reflect.ValueOf(results.InitializeDuplicate),
		"InitializeHandler":     reflect.ValueOf(results.InitializeHandler),
		"InitializeResultQueue": reflect.ValueOf(results.InitializeResultQueue),
		"InitializeResults":     reflect.ValueOf(results.InitializeResults),
		"ResultQueues":          reflect.ValueOf(&results.ResultQueues).<PERSON><PERSON>(),
		"Results":               reflect.ValueOf(&results.Results).<PERSON><PERSON>(),

		// type definitions
		"ResultQueue": reflect.ValueOf((*results.ResultQueue)(nil)),
	}
}

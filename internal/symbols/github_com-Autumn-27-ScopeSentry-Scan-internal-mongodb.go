// Code generated by 'yaegi extract github.com/Autumn-27/ScopeSentry-Scan/internal/mongodb'. DO NOT EDIT.

package symbols

import (
	"github.com/Autumn-27/ScopeSentry-Scan/internal/mongodb"
	"reflect"
)

func init() {
	Symbols["github.com/Autumn-27/ScopeSentry-Scan/internal/mongodb/mongodb"] = map[string]reflect.Value{
		// function, constant and variable definitions
		"Initialize":    reflect.ValueOf(mongodb.Initialize),
		"MongodbClient": reflect.ValueOf(&mongodb.MongodbClient).Elem(),

		// type definitions
		"Client": reflect.ValueOf((*mongodb.Client)(nil)),
	}
}

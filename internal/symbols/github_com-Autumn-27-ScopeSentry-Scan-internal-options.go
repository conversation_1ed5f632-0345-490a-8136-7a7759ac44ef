// Code generated by 'yaegi extract github.com/Autumn-27/ScopeSentry-Scan/internal/options'. DO NOT EDIT.

package symbols

import (
	"github.com/Autumn-27/ScopeSentry-Scan/internal/options"
	"reflect"
)

func init() {
	Symbols["github.com/Autumn-27/ScopeSentry-Scan/internal/options/options"] = map[string]reflect.Value{
		// type definitions
		"PluginOption": reflect.ValueOf((*options.PluginOption)(nil)),
		"TaskOptions":  reflect.ValueOf((*options.TaskOptions)(nil)),
	}
}

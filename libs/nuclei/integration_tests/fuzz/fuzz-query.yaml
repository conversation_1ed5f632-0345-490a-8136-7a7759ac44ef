id: fuzz-query

info:
  name: Basic Fuzz URL Query
  author: pdteam
  severity: info

http:
  - method: GET
    path:
      - "{{BaseURL}}"
    fuzzing:
      - part: query
        type: postfix
        mode: single
        keys: ["id"] 
        fuzz: ["6842'\"><"]
    matchers-condition: and
    matchers:
      - type: word
        part: body
        words:
          - "6842'\"><"
      - type: word
        part: header
        words:
          - "text/html"

builds:
  - binary: gospider
    goos:
      - linux
      - windows
      - freebsd
      - openbsd
      - darwin
    goarch:
      - amd64
      - 386
      - arm
      - arm64
    ignore:
      - goos: darwin
        goarch: 386

archives:
  - name_template: "{{ .ProjectName }}_{{ .Tag }}_{{ .Os }}_{{ .Arch }}"
    replacements:
      darwin: macos
      386: i386
      amd64: x86_64
    wrap_in_directory: true
    format: zip

checksum:
  name_template: "{{ .ProjectName }}_checksums.txt"
snapshot:
  name_template: "{{ .Tag }}"

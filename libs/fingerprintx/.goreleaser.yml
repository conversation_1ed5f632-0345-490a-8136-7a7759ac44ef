# Copyright 2022 Praetorian Security, Inc.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Documentation on this file structure: https://goreleaser.com
project_name: fingerprintx
before:
  hooks:
  - go mod download
builds:
- id: fingerprintx
  main: ./cmd/fingerprintx
  binary: fingerprintx
  env:
  - CGO_ENABLED=0
  goos:
  - linux
  - darwin
  - windows
  goarch:
  - amd64
  - arm64
changelog:
  # skip changelog generation for now
  skip: true
  sort: asc
  filters:
    exclude:
    - '^docs:'
    - '^test:'

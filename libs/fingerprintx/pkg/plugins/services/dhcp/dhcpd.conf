# dhcpd.conf
#
# Sample configuration file for ISC dhcpd
#

option domain-name "example.org";
option domain-name-servers ns1.example.org, ns2.example.org;
default-lease-time 600;
max-lease-time 7200;
#ddns-update-style none;
authoritative;
log-facility local7;

# No service will be given on this subnet, but declaring it allows the dhcp
# server to listen on this network
subnet 0.0.0.0 netmask 0.0.0.0 {
  range ********** ************;
}

# This is a very basic subnet declaration.
subnet ************ netmask *************** {
  range ************* *************;
  option routers rtr-239-0-1.example.org, rtr-239-0-2.example.org;
}

# A slightly different configuration for an internal subnet.
subnet ******** netmask *************** {
  range ********* *********;
  option domain-name-servers ns1.internal.example.org;
  option domain-name "internal.example.org";
  option routers ********;
  option broadcast-address *********;
  default-lease-time 600;
  max-lease-time 7200;
}

# Fixed IP addresses can also be specified for hosts.   These addresses
# should not also be listed as being available for dynamic assignment.
# Hosts for which fixed IP addresses have been specified can boot using
# BOOTP or DHCP.   Hosts for which no fixed address is specified can only
# be booted with DHCP, unless there is an address range on the subnet
# to which a BOOTP client is connected which has the dynamic-bootp flag
# set.
host fantasia {
  hardware ethernet 08:00:07:26:c0:a5;
  fixed-address fantasia.example.com;
}

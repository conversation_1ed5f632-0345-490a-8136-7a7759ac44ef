# Copyright 2022 Praetorian Security, Inc.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

linters:
  disable-all: true
  enable:
    # checks whether HTTP response body is closed successfully
    - bodyclose

    # Errcheck is a program for checking for unchecked errors in go programs.
    # These unchecked errors can be critical bugs in some cases
    - errcheck

    # Gci control golang package import order and make it always deterministic.
    - gci

    # Finds repeated strings that could be replaced by a constant
    - goconst

    # Provides many diagnostics that check for bugs, performance and style issues.
    - gocritic

    # Ensure code has been run through `go fmt`
    - gofmt

    # Inspects source code for security problems
    - gosec

    # Linter for Go source code that specializes in simplifying a code
    - gosimple

    # Vet examines Go source code and reports suspicious constructs, such as
    # Printf calls whose arguments do not align with the format string
    - govet

    # Reports long lines
    - lll

    # Detects when assignments to existing variables are not used
    - ineffassign

    # Revive is a fast, configurable, extensible, flexible, and beautiful
    # linter for Go.
    - revive

    # Staticcheck is a go vet on steroids, applying a ton of static analysis
    # checks
    - staticcheck

    # Like the front-end of a Go compiler, parses and type-checks Go code
    - typecheck

    # Checks Go code for unused constants, variables, functions and types
    - unused

    # Tool for detection of leading and trailing whitespace
    - whitespace

  fast: false


# options for analysis running
run:
  # default concurrency is a available CPU number
  concurrency: 4

  # timeout for analysis, e.g. 30s, 5m, default is 1m
  timeout: 1m

  # exit code when at least one issue was found, default is 1
  issues-exit-code: 1

  # include test files or not, default is true
  tests: true

  # list of build tags, all linters use it. Default is empty list.
  build-tags: []

  # which dirs to skip: issues from them won't be reported;
  # can use regexp here: generated.*, regexp is applied on full path;
  # default value is empty list, but default dirs are skipped independently
  # from this option's value (see skip-dirs-use-default).
  # "/" will be replaced by current OS file path separator to properly work
  # on Windows.
  skip-dirs:
    - _fixtures
    - .bazelcache
    - 3rdparty
    - bazel-*

  # default is true. Enables skipping of directories:
  #   vendor$, third_party$, testdata$, examples$, Godeps$, builtin$
  skip-dirs-use-default: true

  # which files to skip: they will be analyzed, but issues from them
  # won't be reported. Default value is empty list, but there is
  # no need to include all autogenerated files, we confidently recognize
  # autogenerated files. If it's not please let us know.
  # "/" will be replaced by current OS file path separator to properly work
  # on Windows.
  skip-files: []

  # by default isn't set. If set we pass it to "go list -mod={option}". From "go help modules":
  # If invoked with -mod=readonly, the go command is disallowed from the implicit
  # automatic updating of go.mod described above. Instead, it fails when any changes
  # to go.mod are needed. This setting is most useful to check that go.mod does
  # not need updates, such as in a continuous integration and testing system.
  # If invoked with -mod=vendor, the go command assumes that the vendor
  # directory holds the correct copies of dependencies and ignores
  # the dependency descriptions in go.mod.
  modules-download-mode: mod

  # Allow multiple parallel golangci-lint instances running.
  # If false (default) - golangci-lint acquires file lock on start.
  allow-parallel-runners: true


# output configuration options
output:
  # colored-line-number|line-number|json|tab|checkstyle|code-climate|junit-xml|github-actions
  # default is "colored-line-number"
  format: colored-line-number

  # print lines of code with issue, default is true
  print-issued-lines: true

  # print linter name in the end of issue text, default is true
  print-linter-name: true

  # make issues output unique by line, default is true
  uniq-by-line: true

  # add a prefix to the output file references; default is no prefix
  path-prefix: ""

  # sorts results by: filepath, line and column
  sort-results: false


# all available settings of specific linters
linters-settings:

  cyclop:
    # the maximal code complexity to report
    max-complexity: 10
    # the maximal average package complexity. If it's higher than 0.0 (float) the check is enabled (default 0.0)
    package-average: 0.0
    # should ignore tests (default false)
    skip-tests: false

  dogsled:
    # checks assignments with too many blank identifiers; default is 2
    max-blank-identifiers: 2

  dupl:
    # tokens count to trigger issue, 150 by default
    threshold: 100

  errcheck:
    # report about not checking of errors in type assertions: `a := b.(MyStruct)`;
    # default is false: such cases aren't reported by default.
    check-type-assertions: false

    # report about assignment of errors to blank identifier: `num, _ := strconv.Atoi(numStr)`;
    # default is false: such cases aren't reported by default.
    check-blank: false

    # list of functions to exclude from checking, where each entry is a single function to exclude.
    # see https://github.com/kisielk/errcheck#excluding-functions for details
    exclude-functions:
    - github.com/spf13/viper.BindPFlag

  errorlint:
    # Check whether fmt.Errorf uses the %w verb for formatting errors. See the readme for caveats
    errorf: true
    # Check for plain type assertions and type switches
    asserts: true
    # Check for plain error comparisons
    comparison: true

  exhaustive:
    # check switch statements in generated files also
    check-generated: false
    # indicates that switch statements are to be considered exhaustive if a
    # 'default' case is present, even if all enum members aren't listed in the
    # switch
    default-signifies-exhaustive: false

  exhaustivestruct:
    # Struct Patterns is list of expressions to match struct packages and names
    # The struct packages have the form example.com/package.ExampleStruct
    # The matching patterns can use matching syntax from https://pkg.go.dev/path#Match
    # If this list is empty, all structs are tested.
    struct-patterns: []

  forbidigo:
    # Forbid the following identifiers (identifiers are written using regexp):
    forbid:
      - ^print.*$
      - 'fmt\.Print.*'
    # Exclude godoc examples from forbidigo checks.  Default is true.
    exclude_godoc_examples: false

  funlen:
    lines: 60
    statements: 40

  gci:
    # put imports beginning with prefix after 3rd-party packages;
    # only support one prefix
    # if not set, use goimports.local-prefixes
    local-prefixes: github.com/praetorian-inc/snowcat

  gocognit:
    # minimal code complexity to report, 30 by default (but we recommend 10-20)
    min-complexity: 10

  goconst:
    # minimal length of string constant, 3 by default
    min-len: 3
    # minimum occurrences of constant string count to trigger issue, 3 by default
    min-occurrences: 3
    # ignore test files, false by default
    ignore-tests: false
    # look for existing constants matching the values, true by default
    match-constant: true
    # search also for duplicated numbers, false by default
    numbers: false
    # minimum value, only works with goconst.numbers, 3 by default
    min: 3
    # maximum value, only works with goconst.numbers, 3 by default
    max: 3
    # ignore when constant is not used as function argument, true by default
    ignore-calls: true

  gocritic:
    # Which checks should be enabled; can't be combined with 'disabled-checks';
    # See https://go-critic.github.io/overview#checks-overview
    # To check which checks are enabled run `GL_DEBUG=gocritic golangci-lint run`
    # By default list of stable checks is used.
    enabled-checks: []

    # Which checks should be disabled; can't be combined with 'enabled-checks'; default is empty
    disabled-checks:
    - ifElseChain

    # Enable multiple checks by tags, run `GL_DEBUG=gocritic golangci-lint run` to see all tags and checks.
    # Empty list by default. See https://github.com/go-critic/go-critic#usage -> section "Tags".
    enabled-tags: []
    disabled-tags: []

  gocyclo:
    # minimal code complexity to report, 30 by default (but we recommend 10-20)
    min-complexity: 10

  godot:
    # comments to be checked: `declarations`, `toplevel`, or `all`
    scope: all
    # list of regexps for excluding particular comment lines from check
    exclude:
      # example: exclude comments which contain numbers
      # - '[0-9]+'
    # check that each sentence starts with a capital letter
    capital: false

  godox:
    # report any comments starting with keywords, this is useful for TODO or FIXME comments that
    # might be left in the code accidentally and should be resolved before merging
    keywords: # default keywords are TODO, BUG, and FIXME, these can be overwritten by this setting
      # - NOTE
      # - OPTIMIZE # marks code that should be optimized before merging
      # - HACK # marks hack-arounds that should be removed before merging

  gofmt:
    # simplify code: gofmt with `-s` option, true by default
    simplify: true

  gofumpt:
    # Select the Go version to target. The default is `1.15`.
    lang-version: "1.20"

    # Choose whether or not to use the extra rules that are disabled
    # by default
    extra-rules: false

  goheader:
    values:
      const:
        # define here const type values in format k:v, for example:
        # COMPANY: MY COMPANY
      regexp:
        # define here regexp type values, for example
        # AUTHOR: .*@mycompany\.com
    template: # |-
      # put here copyright header template for source code files, for example:
      # Note: {{ YEAR }} is a builtin value that returns the year relative to the current machine time.
      #
      # {{ AUTHOR }} {{ COMPANY }} {{ YEAR }}
      # SPDX-License-Identifier: Apache-2.0

      # Licensed under the Apache License, Version 2.0 (the "License");
      # you may not use this file except in compliance with the License.
      # You may obtain a copy of the License at:

      #   http://www.apache.org/licenses/LICENSE-2.0

      # Unless required by applicable law or agreed to in writing, software
      # distributed under the License is distributed on an "AS IS" BASIS,
      # WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
      # See the License for the specific language governing permissions and
      # limitations under the License.
    template-path:
      # also as alternative of directive 'template' you may put the path to file with the template source

  goimports:
    # put imports beginning with prefix after 3rd-party packages;
    # it's a comma-separated list of prefixes
    local-prefixes: github.com/org/project

  golint:
    # minimal confidence for issues, default is 0.8
    min-confidence: 0.8

  gomnd:
    settings:
      mnd:
        # the list of enabled checks, see https://github.com/tommy-muehle/go-mnd/#checks for description.
        checks: argument,case,condition,operation,return,assign
        # ignored-numbers: 1000
        # ignored-files: magic_.*.go
        # ignored-functions: math.*

  gomoddirectives:
    # Allow local `replace` directives. Default is false.
    replace-local: false
    # List of allowed `replace` directives. Default is empty.
    replace-allow-list:
      - launchpad.net/gocheck
    # Allow to not explain why the version has been retracted in the `retract` directives. Default is false.
    retract-allow-no-explanation: false
    # Forbid the use of the `exclude` directives. Default is false.
    exclude-forbidden: false

  gomodguard:
    allowed:
      modules:                                                        # List of allowed modules
        # - gopkg.in/yaml.v2
      domains:                                                        # List of allowed module domains
        # - golang.org
    blocked:
      modules:                                                        # List of blocked modules
        # - github.com/uudashr/go-module:                             # Blocked module
        #     recommendations:                                        # Recommended modules that should be used instead (Optional)
        #       - golang.org/x/mod
        #     reason: "`mod` is the official go.mod parser library."  # Reason why the recommended module should be used (Optional)
      versions:                                                       # List of blocked module version constraints
        # - github.com/mitchellh/go-homedir:                          # Blocked module with version constraint
        #     version: "< 1.1.0"                                      # Version constraint, see https://github.com/Masterminds/semver#basic-comparisons
        #     reason: "testing if blocked version constraint works."  # Reason why the version constraint exists. (Optional)
      local_replace_directives: false                                 # Set to true to raise lint issues for packages that are loaded from a local path via replace directive

  gosec:
    # To select a subset of rules to run.
    # Available rules: https://github.com/securego/gosec#available-rules
    includes: []
    # To specify a set of rules to explicitly exclude.
    # Available rules: https://github.com/securego/gosec#available-rules
    excludes: []
    # To specify the configuration of rules.
    # The configuration of rules is not fully documented by gosec:
    # https://github.com/securego/gosec#configuration
    # https://github.com/securego/gosec/blob/569328eade2ccbad4ce2d0f21ee158ab5356a5cf/rules/rulelist.go#L60-L102
    # system:
    #   G306: "0600"
    #   G101:
    #     pattern: "(?i)example"
    #     ignore_entropy: false
    #     entropy_threshold: "80.0"
    #     per_char_threshold: "3.0"
    #     truncate: "32"

  gosimple:
    # Select the Go version to target. The default is '1.13'.
    go: "1.15"
    # https://staticcheck.io/docs/options#checks
    checks: [ "all" ]

  govet:
    # report about shadowed variables
    check-shadowing: true

    # settings per analyzer
    # settings:
    #   printf: # analyzer name, run `go tool vet help` to see all analyzers
    #     funcs: # run `go tool vet help printf` to see available settings for `printf` analyzer
    #       - (github.com/golangci/golangci-lint/pkg/logutils.Log).Infof
    #       - (github.com/golangci/golangci-lint/pkg/logutils.Log).Warnf
    #       - (github.com/golangci/golangci-lint/pkg/logutils.Log).Errorf
    #       - (github.com/golangci/golangci-lint/pkg/logutils.Log).Fatalf

    # enable or disable analyzers by name
    # run `go tool vet help` to see all analyzers
    enable-all: true
    disable:
    - fieldalignment
    disable-all: false

  depguard:
    list-type: blacklist
    include-go-root: false
    packages:
      - github.com/sirupsen/logrus
    packages-with-error-message:
      # specify an error message to output when a blacklisted package is used
      - github.com/sirupsen/logrus: "logging is allowed only by logutils.Log"

  ifshort:
    # Maximum length of variable declaration measured in number of lines, after which linter won't suggest using short syntax.
    # Has higher priority than max-decl-chars.
    max-decl-lines: 1
    # Maximum length of variable declaration measured in number of characters, after which linter won't suggest using short syntax.
    max-decl-chars: 30

  importas:
    # if set to `true`, force to use alias.
    no-unaliased: true
    # List of aliases
    alias:
      # using `servingv1` alias for `knative.dev/serving/pkg/apis/serving/v1` package
      - pkg: knative.dev/serving/pkg/apis/serving/v1
        alias: servingv1
      # using `autoscalingv1alpha1` alias for `knative.dev/serving/pkg/apis/autoscaling/v1alpha1` package
      - pkg: knative.dev/serving/pkg/apis/autoscaling/v1alpha1
        alias: autoscalingv1alpha1
      # You can specify the package path by regular expression,
      # and alias by regular expression expansion syntax like below.
      # see https://github.com/julz/importas#use-regular-expression for details
      - pkg: knative.dev/serving/pkg/apis/(\w+)/(v[\w\d]+)
        alias: $1$2

  ireturn:
    # ireturn allows using `allow` and `reject` settings at the same time.
    # Both settings are lists of the keywords and regular expressions matched to interface or package names.
    # keywords:
    # - `empty` for `interface{}`
    # - `error` for errors
    # - `stdlib` for standard library
    # - `anon` for anonymous interfaces

    # By default, it allows using errors, empty interfaces, anonymous interfaces,
    # and interfaces provided by the standard library.
    allow:
    - anon
    - error
    - empty
    - stdlib
    # You can specify idiomatic endings for interface
    - (or|er)$

    # Reject patterns
    reject:
    - github.com\/user\/package\/v4\.Type

  lll:
    # max line length, lines longer will be reported. Default is 120.
    # '\t' is counted as 1 character by default, and can be changed with the tab-width option
    line-length: 120
    # tab width in spaces. Default to 1.
    tab-width: 1

  makezero:
    # Allow only slices initialized with a length of zero. Default is false.
    always: false

  maligned:
    # print struct with more effective memory layout or not, false by default
    suggest-new: true

  misspell:
    # Correct spellings using locale preferences for US or UK.
    # Default is to use a neutral variety of English.
    # Setting locale to US will correct the British spelling of 'colour' to 'color'.
    locale: US
    ignore-words:
      - someword

  nakedret:
    # make an issue if func has more lines of code than this setting and it has naked returns; default is 30
    max-func-lines: 30

  nestif:
    # minimal complexity of if statements to report, 5 by default
    min-complexity: 4

  nilnil:
    # By default, nilnil checks all returned types below.
    checked-types:
      - ptr
      - func
      - iface
      - map
      - chan

  nlreturn:
    # size of the block (including return statement that is still "OK")
    # so no return split required.
    block-size: 1

  nolintlint:
    # Enable to ensure that nolint directives are all used. Default is true.
    allow-unused: true
    # Disable to ensure that nolint directives don't have a leading space. Default is true.
    allow-leading-space: true
    # Exclude following linters from requiring an explanation.  Default is [].
    allow-no-explanation: []
    # Enable to require an explanation of nonzero length after each nolint directive. Default is false.
    require-explanation: true
    # Enable to require nolint directives to mention the specific linter being suppressed. Default is false.
    require-specific: false

  prealloc:
    # XXX: we don't recommend using this linter before doing performance profiling.
    # For most programs usage of prealloc will be a premature optimization.

    # Report preallocation suggestions only on simple loops that have no returns/breaks/continues/gotos in them.
    # True by default.
    simple: true
    range-loops: true # Report preallocation suggestions on range loops, true by default
    for-loops: false # Report preallocation suggestions on for loops, false by default

  promlinter:
    # Promlinter cannot infer all metrics name in static analysis.
    # Enable strict mode will also include the errors caused by failing to parse the args.
    strict: false
    # Please refer to https://github.com/yeya24/promlinter#usage for detailed usage.
    disabled-linters:
    #  - "Help"
    #  - "MetricUnits"
    #  - "Counter"
    #  - "HistogramSummaryReserved"
    #  - "MetricTypeInName"
    #  - "ReservedChars"
    #  - "CamelCase"
    #  - "lintUnitAbbreviations"

  predeclared:
    # comma-separated list of predeclared identifiers to not report on
    ignore: ""
    # include method names and field names (i.e., qualified names) in checks
    q: false

  rowserrcheck:
    packages: []

  revive:
    # see https://github.com/mgechev/revive#available-rules for details.
    rules:
    - name: context-as-argument
    - name: context-keys-type
    - name: dot-imports
    - name: error-return
    - name: error-strings
    - name: error-naming
    - name: if-return
    - name: increment-decrement
    - name: var-naming
    - name: var-declaration
    - name: package-comments
    - name: range
    - name: receiver-naming
    - name: time-naming
    - name: unexported-return
    - name: indent-error-flow
    - name: errorf
    - name: empty-block
    - name: superfluous-else
    - name: unused-parameter
    - name: unreachable-code
    - name: redefines-builtin-id

  staticcheck:
    # Select the Go version to target. The default is '1.13'.
    go: "1.15"
    # https://staticcheck.io/docs/options#checks
    checks: [ "all" ]

  stylecheck:
    # Select the Go version to target. The default is '1.13'.
    go: "1.15"
    # https://staticcheck.io/docs/options#checks
    checks: [ "all" ]
    # https://staticcheck.io/docs/options#dot_import_whitelist
    dot-import-whitelist: []
    # https://staticcheck.io/docs/options#initialisms
    initialisms: [ "ACL", "API", "ASCII", "CPU", "CSS", "DNS", "EOF", "GUID", "HTML", "HTTP", "HTTPS", "ID", "IP", "JSON", "QPS", "RAM", "RPC", "SLA", "SMTP", "SQL", "SSH", "TCP", "TLS", "TTL", "UDP", "UI", "GID", "UID", "UUID", "URI", "URL", "UTF8", "VM", "XML", "XMPP", "XSRF", "XSS" ]
    # https://staticcheck.io/docs/options#http_status_code_whitelist
    http-status-code-whitelist: [ "200", "400", "404", "500" ]

  tagliatelle:
    # check the struck tag name case
    case:
      # use the struct field name to check the name of the struct tag
      use-field-name: true
      rules:
        # any struct tag type can be used.
        # support string case: `camel`, `pascal`, `kebab`, `snake`, `goCamel`, `goPascal`, `goKebab`, `goSnake`, `upper`, `lower`
        json: camel
        yaml: camel
        xml: camel
        bson: camel
        avro: snake
        mapstructure: kebab

  unparam:
    # Inspect exported functions, default is false. Set to true if no external program/library imports your code.
    # XXX: if you enable this setting, unparam will report a lot of false-positives in text editors:
    # if it's called for subdir of a project it can't find external interfaces. All text editor integrations
    # with golangci-lint call it on a directory with the changed file.
    check-exported: false

  unused:
    # Select the Go version to target. The default is '1.13'.
    go: "1.15"

  whitespace:
    # multi-if: false   # Enforces newlines (or comments) after every multi-line if statement
    # multi-func: false # Enforces newlines (or comments) after every multi-line function signature

  wrapcheck:
    # An array of strings that specify substrings of signatures to ignore.
    # If this set, it will override the default set of ignored signatures.
    # See https://github.com/tomarrell/wrapcheck#configuration for more information.

  wsl:
    # See https://github.com/bombsimon/wsl/blob/master/doc/configuration.md for
    # documentation of available settings. These are the defaults for
    # `golangci-lint`.
    allow-assign-and-anything: false
    allow-assign-and-call: true
    allow-cuddle-declarations: false
    allow-multiline-assign: true
    allow-separated-leading-comment: false
    allow-trailing-comment: false
    force-case-trailing-whitespace: 0
    force-err-cuddling: false
    force-short-decl-cuddling: false
    strict-append: true


issues:
  # List of regexps of issue texts to exclude, empty list by default.
  # But independently from this option we use default exclude patterns,
  # it can be disabled by `exclude-use-default: false`. To list all
  # excluded by default patterns execute `golangci-lint run --help`
  exclude: []

  # Excluding configuration per-path, per-linter, per-text and per-source
  exclude-rules:
    # Exclude lll issues for long lines with go:generate
    - linters:
        - lll
      source: "^//go:generate "

  # Independently from option `exclude` we use default exclude patterns,
  # it can be disabled by this option. To list all
  # excluded by default patterns execute `golangci-lint run --help`.
  # Default value for this option is true.
  exclude-use-default: true

  # The default value is false. If set to true exclude and exclude-rules
  # regular expressions become case sensitive.
  exclude-case-sensitive: false

  # The list of ids of default excludes to include or disable. By default it's empty.
  include: []

  # Maximum issues count per one linter. Set to 0 to disable. Default is 50.
  max-issues-per-linter: 0

  # Maximum count of issues with the same text. Set to 0 to disable. Default is 3.
  max-same-issues: 0

  # Show only new issues: if there are unstaged changes or untracked files,
  # only those changes are analyzed, else only changes in HEAD~ are analyzed.
  # It's a super-useful option for integration of golangci-lint into existing
  # large codebase. It's not practical to fix all existing issues at the moment
  # of integration: much better don't allow issues in new code.
  # Default is false.
  new: false

  # Show only new issues created after git revision `REV`
  # new-from-rev: REV

  # Show only new issues created in git patch with set file path.
  # new-from-patch: path/to/patch/file

  # Fix found issues (if it's supported by the linter)
  fix: false

severity:
  # Default value is empty string.
  # Set the default severity for issues. If severity rules are defined and the issues
  # do not match or no severity is provided to the rule this will be the default
  # severity applied. Severities should match the supported severity names of the
  # selected out format.
  # - Code climate: https://docs.codeclimate.com/docs/issues#issue-severity
  # -   Checkstyle: https://checkstyle.sourceforge.io/property_types.html#severity
  # -       Github: https://help.github.com/en/actions/reference/workflow-commands-for-github-actions#setting-an-error-message
  default-severity: error

  # The default value is false.
  # If set to true severity-rules regular expressions become case sensitive.
  case-sensitive: false

  # Default value is empty list.
  # When a list of severity rules are provided, severity information will be added to lint
  # issues. Severity rules have the same filtering capability as exclude rules except you
  # are allowed to specify one matcher per severity rule.
  # Only affects out formats that support setting severity information.
  rules:
    - linters:
      - dupl
      severity: info

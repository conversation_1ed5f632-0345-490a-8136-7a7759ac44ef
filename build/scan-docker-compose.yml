version: '3'
services:
  scan:
    image: autumn27/scopesentry-scan:latest
    network_mode: host
    container_name: scopesentry-scan
    restart: always
    ulimits:
      core:
        soft: 0
        hard: 0
    environment:
      NodeName: node-test
      TimeZoneName: Asia/Shanghai
      MONGODB_IP: ${MONGODB_IP}
      MONGODB_PORT: ${MONGODB_PORT}
      MONGODB_DATABASE: ScopeSentry
      MONGODB_USER: ${MONGO_INITDB_ROOT_USERNAME}
      MONGODB_PASSWORD: ${MONGO_INITDB_ROOT_PASSWORD}
      REDIS_IP: ${REDIS_IP}
      REDIS_PORT: ${REDIS_PORT}
      REDIS_PASSWORD: ${REDIS_PASSWORD}

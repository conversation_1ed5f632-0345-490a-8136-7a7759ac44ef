version: "3.5"

services:
  scope-sentry:
    image: autumn27/scopesentry:latest
    container_name: scope-sentry
    restart: always
    ports:
      - "8082:8082"
    environment:
      TIMEZONE: Asia/Shanghai
      MONGODB_IP: ${MONGODB_IP}
      MONGODB_PORT: ${MONGODB_PORT}
      MONGODB_DATABASE: ScopeSentry
      MONGODB_USER: ${MONGO_INITDB_ROOT_USERNAME}
      MONGODB_PASSWORD: ${MONGO_INITDB_ROOT_PASSWORD}
      REDIS_IP: ${REDIS_IP}
      REDIS_PORT: ${REDIS_PORT}
      REDIS_PASSWORD: ${REDIS_PASSWORD}
